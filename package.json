{"name": "car-listing-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.6", "@types/react-datepicker": "^7.0.0", "aws-sdk": "^2.1692.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "lucide-react": "^0.487.0", "next": "15.2.4", "next-themes": "^0.2.1", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-day-picker": "^8.9.1", "react-dom": "^19.0.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-range-slider-input": "^3.2.1", "sonner": "^1.4.0", "tailwind-merge": "^3.3.0", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "zod": "^3.22.4"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "tailwindcss": "^4", "typescript": "^5"}}