// Simple test script to verify video download functionality
const fetch = require('node-fetch');

async function testDownload() {
  try {
    console.log('Testing video download API...');
    
    const response = await fetch('http://localhost:3001/api/download-clip', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        youtubeUrl: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        startTime: 10,
        endTime: 20,
        title: 'Test Clip',
        quality: 'low'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    console.log('Download started:', result);

    if (result.success) {
      console.log('✅ API is working correctly');
      console.log('Clip ID:', result.clipId);
      
      // Test progress checking
      setTimeout(async () => {
        try {
          const progressResponse = await fetch(`http://localhost:3001/api/download-clip?clipId=${result.clipId}`);
          const progress = await progressResponse.json();
          console.log('Progress check:', progress);
        } catch (error) {
          console.error('Progress check failed:', error.message);
        }
      }, 1000);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDownload();
