# Environment Variables for YouTube Repurpose Web

# Application Settings
NODE_ENV=production
PORT=3000
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Storage Configuration
STORAGE_PROVIDER=local
# Options: 'local' or 's3'

# Local Storage Settings (when STORAGE_PROVIDER=local)
LOCAL_STORAGE_PATH=/app/public/clips
LOCAL_BASE_URL=https://your-domain.com

# S3 Storage Settings (when STORAGE_PROVIDER=s3)
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-bucket-name
S3_PUBLIC_URL=https://your-bucket.s3.amazonaws.com

# Video Processing Settings
DEFAULT_VIDEO_QUALITY=medium
# Options: 'high', 'medium', 'low'

MAX_CLIP_DURATION=300
# Maximum clip duration in seconds (5 minutes)

DOWNLOAD_TIMEOUT=600000
# Download timeout in milliseconds (10 minutes)

# Security Settings
MAX_DOWNLOADS_PER_HOUR=10
# Rate limiting for downloads per IP

# Webhook Settings (optional)
WEBHOOK_SECRET=your_webhook_secret_here

# Debug Settings
DEBUG_VIDEO_DOWNLOADS=false
# Set to true for verbose logging
