import { useState, useCallback, useRef } from 'react';
import { Clip<PERSON>nalyzer, ClipCandidate, AnalysisConfig } from '@/lib/clipAnalysis';

export interface UseClipAnalysisReturn {
  isAnalyzing: boolean;
  progress: number;
  error: string | null;
  candidates: ClipCandidate[];
  analyzeVideo: (duration: number, generateCandidate?: (start: number, end: number, totalDuration: number) => ClipCandidate) => Promise<void>;
  clearResults: () => void;
}

export function useClipAnalysis(): UseClipAnalysisReturn {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [candidates, setCandidates] = useState<ClipCandidate[]>([]);
  
  const analyzerRef = useRef<ClipAnalyzer | null>(null);

  const analyzeVideo = useCallback(async (
    duration: number,
    generateCandidate?: (start: number, end: number, totalDuration: number) => ClipCandidate
  ) => {
    if (!duration || duration <= 0) {
      setError('Invalid video duration');
      return;
    }

    setIsAnalyzing(true);
    setProgress(0);
    setError(null);
    setCandidates([]);

    try {
      // Configuration
      const clipDuration = 30;
      const overlap = 15;
      const step = clipDuration - overlap;
      const minClipLength = 10;

      const totalWindows = Math.ceil((duration - minClipLength) / step);
      const results: ClipCandidate[] = [];

      let processedWindows = 0;

      // Generate analysis windows
      for (let start = 0; start < duration - minClipLength; start += step) {
        const end = Math.min(start + clipDuration, duration);

        if (end - start < minClipLength) continue;

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 150));

        // Generate candidate using provided function or default
        const candidate = generateCandidate
          ? generateCandidate(start, end, duration)
          : generateDefaultCandidate(start, end, duration);

        results.push(candidate);

        processedWindows++;
        setProgress((processedWindows / totalWindows) * 100);
      }

      // Sort by score and take top candidates
      const topCandidates = results
        .sort((a, b) => b.score - a.score)
        .slice(0, 8);

      setCandidates(topCandidates);

    } catch (err) {
      console.error('Video analysis failed:', err);
      setError(err instanceof Error ? err.message : 'Analysis failed');
    } finally {
      setIsAnalyzing(false);
      setProgress(100);
    }
  }, []);

  // Generate a default clip candidate
  const generateDefaultCandidate = (start: number, end: number, totalDuration: number): ClipCandidate => {
    const segmentDuration = end - start;
    const position = start / totalDuration;

    // Create realistic scoring based on common video patterns
    let audioEnergy = 0.3 + Math.random() * 0.4;
    let sceneChanges = 0.2 + Math.random() * 0.3;
    let motionLevel = 0.2 + Math.random() * 0.3;

    // Boost scores for certain patterns
    if (position < 0.1 || position > 0.8) {
      audioEnergy += 0.2;
      motionLevel += 0.15;
    }

    if (position > 0.3 && position < 0.7) {
      audioEnergy += 0.1;
      sceneChanges += 0.2;
    }

    if (segmentDuration >= 20 && segmentDuration <= 40) {
      audioEnergy += 0.1;
      sceneChanges += 0.1;
      motionLevel += 0.1;
    }

    // Add randomness for variety
    const randomBoost = Math.random() * 0.3;
    audioEnergy = Math.min(1, audioEnergy + randomBoost * 0.3);
    sceneChanges = Math.min(1, sceneChanges + randomBoost * 0.3);
    motionLevel = Math.min(1, motionLevel + randomBoost * 0.4);

    // Calculate composite score
    const score = audioEnergy * 0.4 + sceneChanges * 0.3 + motionLevel * 0.3;

    // Generate reasons
    const reasons: string[] = [];
    if (audioEnergy > 0.7) reasons.push('High audio energy');
    else if (audioEnergy > 0.5) reasons.push('Good audio activity');

    if (sceneChanges > 0.6) reasons.push('Dynamic scene changes');
    else if (sceneChanges > 0.4) reasons.push('Visual variety');

    if (motionLevel > 0.6) reasons.push('High motion content');
    else if (motionLevel > 0.4) reasons.push('Active content');

    if (position < 0.15) reasons.push('Strong opening');
    if (position > 0.8) reasons.push('Compelling ending');
    if (segmentDuration >= 25 && segmentDuration <= 35) reasons.push('Optimal length');

    if (reasons.length === 0) reasons.push('Moderate activity');

    return {
      start,
      end,
      score,
      reasons,
      audioEnergy,
      sceneChanges,
      motionLevel,
    };
  };

  const clearResults = useCallback(() => {
    setCandidates([]);
    setError(null);
    setProgress(0);
  }, []);

  return {
    isAnalyzing,
    progress,
    error,
    candidates,
    analyzeVideo,
    clearResults,
  };
}
