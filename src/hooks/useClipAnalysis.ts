import { useState, useCallback, useRef } from 'react';
import { ClipAnalyzer, ClipCandidate, AnalysisConfig } from '@/lib/clipAnalysis';

export interface UseClipAnalysisReturn {
  isAnalyzing: boolean;
  progress: number;
  error: string | null;
  candidates: ClipCandidate[];
  analyzeVideo: (videoElement: HTMLVideoElement, config?: Partial<AnalysisConfig>) => Promise<void>;
  clearResults: () => void;
}

export function useClipAnalysis(): UseClipAnalysisReturn {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [candidates, setCandidates] = useState<ClipCandidate[]>([]);
  
  const analyzerRef = useRef<ClipAnalyzer | null>(null);

  const analyzeVideo = useCallback(async (
    videoElement: HTMLVideoElement,
    configOverrides?: Partial<AnalysisConfig>
  ) => {
    if (!videoElement || !videoElement.duration) {
      setError('Invalid video element or video not loaded');
      return;
    }

    setIsAnalyzing(true);
    setProgress(0);
    setError(null);
    setCandidates([]);

    try {
      // Initialize analyzer if not already done
      if (!analyzerRef.current) {
        analyzerRef.current = new ClipAnalyzer();
      }

      // Create config with overrides
      const defaultConfig: AnalysisConfig = {
        clipDuration: 30,
        overlap: 15,
        minClipLength: 10,
        maxClipLength: 60,
        audioWeight: 0.4,
        visualWeight: 0.3,
        contentWeight: 0.3,
      };

      const config = { ...defaultConfig, ...configOverrides };

      // Store original video state
      const originalTime = videoElement.currentTime;
      const originalPaused = videoElement.paused;

      // Pause video during analysis
      videoElement.pause();

      // Progress tracking
      const duration = videoElement.duration;
      const windowSize = config.clipDuration;
      const overlap = config.overlap;
      const step = windowSize - overlap;
      const totalWindows = Math.ceil((duration - config.minClipLength) / step);

      let processedWindows = 0;

      // Custom analyzer with progress tracking
      const progressTrackingAnalyzer = new ClipAnalyzer();
      
      // Override the analyzeVideo method to track progress
      const originalAnalyzeVideo = progressTrackingAnalyzer.analyzeVideo.bind(progressTrackingAnalyzer);
      progressTrackingAnalyzer.analyzeVideo = async (video, analysisConfig) => {
        const candidates: ClipCandidate[] = [];
        
        for (let start = 0; start < duration - config.minClipLength; start += step) {
          const end = Math.min(start + windowSize, duration);
          
          if (end - start < config.minClipLength) continue;
          
          // Simulate segment analysis (you'd call the actual analyzeSegment method here)
          const candidate = await this.simulateSegmentAnalysis(video, start, end, analysisConfig);
          candidates.push(candidate);
          
          processedWindows++;
          setProgress((processedWindows / totalWindows) * 100);
        }
        
        return candidates
          .sort((a, b) => b.score - a.score)
          .slice(0, 10);
      };

      const results = await analyzerRef.current.analyzeVideo(videoElement, config);
      
      setCandidates(results);

      // Restore original video state
      videoElement.currentTime = originalTime;
      if (!originalPaused) {
        await videoElement.play();
      }

    } catch (err) {
      console.error('Video analysis failed:', err);
      setError(err instanceof Error ? err.message : 'Analysis failed');
    } finally {
      setIsAnalyzing(false);
      setProgress(100);
    }
  }, []);

  // Simulate segment analysis for progress tracking
  const simulateSegmentAnalysis = async (
    video: HTMLVideoElement,
    start: number,
    end: number,
    config: AnalysisConfig
  ): Promise<ClipCandidate> => {
    // This is a simplified version for demonstration
    // In practice, you'd use the actual analysis methods
    
    // Simulate some processing time
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Generate mock scores based on position and duration
    const duration = end - start;
    const position = start / video.duration;
    
    // Favor clips in the middle of the video and with good duration
    const positionScore = 1 - Math.abs(0.5 - position) * 2;
    const durationScore = Math.min(duration / 30, 1);
    
    // Add some randomness to simulate real analysis
    const randomFactor = 0.5 + Math.random() * 0.5;
    
    const audioEnergy = positionScore * randomFactor;
    const sceneChanges = durationScore * randomFactor;
    const motionLevel = (positionScore + durationScore) / 2 * randomFactor;
    
    const score = (
      audioEnergy * config.audioWeight +
      sceneChanges * config.visualWeight +
      motionLevel * config.contentWeight
    );

    const reasons: string[] = [];
    if (audioEnergy > 0.7) reasons.push('High audio energy');
    if (sceneChanges > 0.6) reasons.push('Dynamic scene changes');
    if (motionLevel > 0.6) reasons.push('High motion content');
    if (reasons.length === 0) reasons.push('Moderate activity');

    return {
      start,
      end,
      score,
      reasons,
      audioEnergy,
      sceneChanges,
      motionLevel,
    };
  };

  const clearResults = useCallback(() => {
    setCandidates([]);
    setError(null);
    setProgress(0);
  }, []);

  return {
    isAnalyzing,
    progress,
    error,
    candidates,
    analyzeVideo,
    clearResults,
  };
}
