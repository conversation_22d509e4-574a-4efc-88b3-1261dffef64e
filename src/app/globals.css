@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  --sidebar-primary: oklch(0.21 0.006 285.885);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.967 0.001 286.375);
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  --sidebar-border: oklch(0.92 0.004 286.32);
  --sidebar-ring: oklch(0.705 0.015 286.067);
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.21 0.006 285.885);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.21 0.006 285.885);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.92 0.004 286.32);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.274 0.006 286.033);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.274 0.006 286.033);
  --muted-foreground: oklch(0.705 0.015 286.067);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.552 0.016 285.938);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.21 0.006 285.885);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.274 0.006 286.033);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.552 0.016 285.938);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for the range slider */
.slider-track {
  height: 6px;
  margin: 16px 0;
}

.slider-track .range-slider__thumb {
  width: 18px;
  height: 18px;
  background-color: white;
  border: 2px solid #97D700;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-track .range-slider__range {
  background-color: #97D700;
  height: 6px;
}

.slider-track .range-slider__track {
  height: 6px;
  background-color: #e5e7eb;
}

.dark .slider-track .range-slider__track {
  background-color: #333333;
}

/* Custom styles for the YouTube Clipper */
.yt-clipper-input {
  background-color: #1E1E1E;
  border: 1px solid #333333;
  color: white;
}

.yt-clipper-button {
  background-color: #97D700;
  color: #121212;
}

.yt-clipper-button:hover {
  background-color: #85bd00;
}

/* Custom styles for react-datepicker */
.react-datepicker {
  font-family: var(--font-sans);
  background-color: #FFFFFF;
  border: 1px solid #e5e7eb;
  border-radius: var(--radius);
  color: #000000;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.dark .react-datepicker {
  background-color: #1E1E1E;
  border-color: #333333;
  color: #FFFFFF;
}

.react-datepicker__header {
  background-color: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.5rem;
}

.dark .react-datepicker__header {
  background-color: #252525;
  border-bottom-color: #333333;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  color: #000000;
  font-weight: 600;
}

.dark .react-datepicker__current-month,
.dark .react-datepicker-time__header,
.dark .react-datepicker-year-header {
  color: #FFFFFF;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  color: #000000;
}

.dark .react-datepicker__day-name,
.dark .react-datepicker__day,
.dark .react-datepicker__time-name {
  color: #FFFFFF;
}

.react-datepicker__day:hover,
.react-datepicker__month-text:hover,
.react-datepicker__quarter-text:hover,
.react-datepicker__year-text:hover {
  background-color: #f3f4f6;
}

.dark .react-datepicker__day:hover,
.dark .react-datepicker__month-text:hover,
.dark .react-datepicker__quarter-text:hover,
.dark .react-datepicker__year-text:hover {
  background-color: #333333;
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--selected,
.react-datepicker__year-text--keyboard-selected {
  background-color: #97D700 !important;
  color: #121212 !important;
  font-weight: bold;
}

.react-datepicker__day--disabled,
.react-datepicker__month-text--disabled,
.react-datepicker__quarter-text--disabled,
.react-datepicker__year-text--disabled {
  color: #9ca3af;
}

.dark .react-datepicker__day--disabled,
.dark .react-datepicker__month-text--disabled,
.dark .react-datepicker__quarter-text--disabled,
.dark .react-datepicker__year-text--disabled {
  color: #4b5563;
}

.react-datepicker__time-container {
  border-left: 1px solid #e5e7eb;
}

.dark .react-datepicker__time-container {
  border-left-color: #333333;
}

.react-datepicker__time-container .react-datepicker__time {
  background-color: #FFFFFF;
}

.dark .react-datepicker__time-container .react-datepicker__time {
  background-color: #1E1E1E;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
  background-color: #FFFFFF;
}

.dark .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box {
  background-color: #1E1E1E;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
  color: #000000;
  padding: 8px 16px;
  height: auto !important;
}

.dark .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
  color: #FFFFFF;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
  background-color: #f3f4f6;
}

.dark .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
  background-color: #333333;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  background-color: #97D700 !important;
  color: #121212 !important;
  font-weight: bold;
}

/* Fix time list scrollbar */
.react-datepicker__time-list {
  scrollbar-width: thin;
  scrollbar-color: #9ca3af transparent;
}

.dark .react-datepicker__time-list {
  scrollbar-color: #4b5563 #1E1E1E;
}

.react-datepicker__time-list::-webkit-scrollbar {
  width: 6px;
}

.react-datepicker__time-list::-webkit-scrollbar-track {
  background: transparent;
}

.dark .react-datepicker__time-list::-webkit-scrollbar-track {
  background: #1E1E1E;
}

.react-datepicker__time-list::-webkit-scrollbar-thumb {
  background-color: #9ca3af;
  border-radius: 3px;
}

.dark .react-datepicker__time-list::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

/* Fix input text color */
.react-datepicker__input-container input {
  color: #000000 !important;
  background-color: #FFFFFF !important;
}

.dark .react-datepicker__input-container input {
  color: #FFFFFF !important;
  background-color: #1E1E1E !important;
}
